package service

import (
	"sync"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/repository"
	"github.com/dsoplabs/dinbora-backend/internal/cache" // Import cache package
	"github.com/dsoplabs/dinbora-backend/internal/service/aiassistant"
	"github.com/dsoplabs/dinbora-backend/internal/service/auth"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/investmentcategory"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/ticker"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trophy"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/service/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/service/league" // Added league service import
	"github.com/dsoplabs/dinbora-backend/internal/service/notification/sendgrid"
	"github.com/dsoplabs/dinbora-backend/internal/service/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/rbac"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/dsoplabs/dinbora-backend/internal/service/vault"
	_sendgrid "github.com/sendgrid/sendgrid-go"
)

type ServiceRegistry struct {
	Auth auth.Service

	// Billing

	// Content
	InvestmentCategory investmentcategory.Service
	Ticker             ticker.Service
	Trail              trail.Service
	Trophy             trophy.Service
	Wallet             wallet.Service

	Dashboard  dashboard.Service
	Dreamboard dreamboard.Service
	//Facebook facebook.Service
	FinancialDNA   financialdna.Service
	FinancialSheet financialsheet.Service
	League         league.Service // Added League service
	//Google   google.Service
	Progression progression.Service

	// Notification
	Sendgrid sendgrid.Service

	// Storage
	S3 s3.Service

	// AI
	AIAssistant aiassistant.Service

	// RBAC
	RBAC rbac.Service

	User  user.Service
	Vault vault.Service
}

type ServiceContainer struct {
	services       map[string]interface{}
	repos          *repository.RepositoryRegistry
	mu             sync.RWMutex
	sendgridClient *_sendgrid.Client
}

func NewContainer(repos *repository.RepositoryRegistry, sendgridClient *_sendgrid.Client) *ServiceContainer {
	return &ServiceContainer{
		services:       make(map[string]interface{}),
		repos:          repos,
		sendgridClient: sendgridClient,
	}
}

func (sc *ServiceContainer) Register(name string, service interface{}) {
	sc.mu.Lock()
	defer sc.mu.Unlock()
	sc.services[name] = service
}

func (sc *ServiceContainer) Get(name string) interface{} {
	sc.mu.RLock()
	defer sc.mu.RUnlock()
	return sc.services[name]
}

func (sc *ServiceContainer) Initialize() *ServiceRegistry {
	// Lazy initialization of Services
	lazyInitService := func(name string, initFunc func() interface{}) interface{} {
		if existing := sc.Get(name); existing != nil {
			return existing
		}
		service := initFunc()
		sc.Register(name, service)
		return service
	}

	// Initialize Cache Service
	// Use reasonable defaults: 1 hour default expiration, 10 minutes cleanup interval
	cacheService := cache.NewInMemoryCache(1*time.Hour, 10*time.Minute)

	// Single dependency Services
	// Content
	investmentCategoryService := lazyInitService("investmentcategory", func() interface{} {
		return investmentcategory.New(sc.repos.Investimentcategory)
	}).(investmentcategory.Service)
	tickerService := lazyInitService("ticker", func() interface{} {
		return ticker.New(sc.repos.Ticker)
	}).(ticker.Service)
	trailService := lazyInitService("trail", func() interface{} {
		// Pass the initialized cache service to trail.New
		return trail.New(sc.repos.Trail, cacheService)
	}).(trail.Service)
	trophyService := lazyInitService("trophy", func() interface{} {
		return trophy.New(sc.repos.Trophy)
	}).(trophy.Service)

	// Notification
	sendgridService := lazyInitService("sendgrid", func() interface{} {
		return sendgrid.New(sc.sendgridClient)
	}).(sendgrid.Service)

	// Storage
	s3Service := lazyInitService("s3", func() interface{} {
		return s3.New()
	}).(s3.Service)

	vaultService := lazyInitService("vault", func() interface{} {
		return vault.New(sc.repos.Vault)
	}).(vault.Service)

	// RBAC Service (no dependencies)
	rbacService := lazyInitService("rbac", func() interface{} {
		return rbac.New()
	}).(rbac.Service)

	// Multi dependency Services
	// Billing

	// Content
	walletService := lazyInitService("wallet", func() interface{} {
		return wallet.New(sc.repos.Wallet, tickerService)
	}).(wallet.Service)

	// Feature Services
	dreamboardService := lazyInitService("dreamboard", func() interface{} {
		return dreamboard.New(sc.repos.Dreamboard, sc.repos.FinancialSheet)
	}).(dreamboard.Service)

	leagueService := lazyInitService("league", func() interface{} {
		return league.New(sc.repos.League) // Corrected League service constructor
	}).(league.Service)

	financialSheetService := lazyInitService("financialsheet", func() interface{} {
		return financialsheet.New(sc.repos.FinancialSheet, sc.repos.Dreamboard, leagueService) // Pass leagueService
	}).(financialsheet.Service)
	dashboardService := lazyInitService("dashboard", func() interface{} {
		return dashboard.New(sc.repos.Dashboard, financialSheetService)
	}).(dashboard.Service)
	financialDNAService := lazyInitService("financialdna", func() interface{} {
		return financialdna.New(sc.repos.FinancialDNA, s3Service)
	}).(financialdna.Service)

	progressionService := lazyInitService("progression", func() interface{} {
		// Pass cacheService to progression.New
		return progression.New(sc.repos.Progression, trailService, trophyService, vaultService, cacheService)
	}).(progression.Service)

	userService := lazyInitService("user", func() interface{} {
		return user.New(sc.repos.User, dreamboardService, financialDNAService, financialSheetService, progressionService, vaultService, walletService, rbacService)
	}).(user.Service)

	// Authorization Services
	authService := lazyInitService("auth", func() interface{} {
		return auth.New(userService, sendgridService, s3Service)
	}).(auth.Service)

	// AI Assistant Service
	aiAssistantService := lazyInitService("aiassistant", func() interface{} {
		return aiassistant.New(
			userService,
			dreamboardService,
			financialDNAService,
			financialSheetService,
			progressionService,
			vaultService,
			walletService,
			trophyService,
			trailService,
		)
	}).(aiassistant.Service)

	return &ServiceRegistry{
		Auth:               authService,
		InvestmentCategory: investmentCategoryService,
		Ticker:             tickerService,
		Trail:              trailService,
		Trophy:             trophyService,
		Wallet:             walletService,
		Dashboard:          dashboardService,
		Dreamboard:         dreamboardService,
		//Facebook:           facebookService,
		FinancialDNA:   financialDNAService,
		FinancialSheet: financialSheetService,
		League:         leagueService, // Added League service to registry
		//Google:             googleService,
		Sendgrid:    sendgridService,
		Progression: progressionService,
		S3:          s3Service,
		AIAssistant: aiAssistantService, // Added AIAssistant service to registry
		RBAC:        rbacService,        // Added RBAC service to registry
		User:        userService,
		Vault:       vaultService,
	}
}

// Implementation of a Service using the container approach, evaluate in the future if we want to have dynamic service/controller/repository allocation.
// investmentCategoryService := lazyInitService("investmentcategory", func() interface{} {
// 	investmentCategoryRepo := sc.repos.Get("investmentcategory").(_investmentcategory.Repository)
// 	return investmentcategory.New(investmentCategoryRepo)
// }).(investmentcategory.Service)
